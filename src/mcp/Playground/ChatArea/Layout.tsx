import styled from '@emotion/styled';
import {ReactNode} from 'react';
import chatBg from '@/assets/mcp/chatBg.png';
import inputBg from '@/assets/mcp/inputBg.png';
import welcomeBg from '@/assets/mcp/welcomeBg.png';

const ChatWrapper = styled.div`
    height: 100%;
    width: 100%;
    background-image: url(${chatBg});
    background-size: 100% auto;
    background-position: bottom;
    background-repeat: no-repeat;
    background-attachment: fixed;
    overflow-y: hidden;
    overflow-x: auto;
`;

const ChatContainer = styled.div<{ visible: boolean }>`
    background-image: ${props =>
        (props.visible ? `url(${welcomeBg})` : 'none')};
    background-size: 100% auto;
    background-position: top;
    background-repeat: no-repeat;
    background-attachment: fixed;
    height: 100%;
    width: 100%;
    position: relative;
    min-width: 930px;
    padding-bottom: 102px;
`;

const MessagesContainer = styled.div`
    height: 100%;
    width: 100%;
    overflow: auto;
    scrollbar-width: none;
`;

const InputContainer = styled.div`
    width: 100%;
    padding: 12px 0;
    position: absolute;
    bottom: 0px;
    left: 0;
    right: 0;
    background-image: url(${inputBg});
    background-position: bottom;
    background-size: 100% auto;
    z-index: 10;
`;

const ContentWrapper = styled.div`
    height: 100%;
    min-width: 830px;
    max-width: 80%;
    margin: 0 auto;
`;

const MessagePanel = styled.div<{ hasMessages: boolean }>`
    margin-left: ${props => (props.hasMessages ? '-50px' : '0')};
`;

const InputPanel = styled.div`
    min-width: 830px;
    max-width: 80%;
    margin: 0 auto;
`;

interface Props {
    messagePanel: ReactNode;
    inputPanel: ReactNode;
    hasMessages?: boolean;
}

export default function ChatLayout({
    messagePanel,
    inputPanel,
    hasMessages = false,
}: Props) {
    return (
        <ChatWrapper>
            <ChatContainer visible={!hasMessages}>
                <MessagesContainer>
                    <ContentWrapper>
                        <MessagePanel hasMessages={hasMessages}>
                            {messagePanel}
                        </MessagePanel>
                    </ContentWrapper>
                </MessagesContainer>
                <InputContainer>
                    <InputPanel>{inputPanel}</InputPanel>
                </InputContainer>
            </ChatContainer>
        </ChatWrapper>
    );
}
