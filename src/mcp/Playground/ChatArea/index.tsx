import {useEffect, useRef, useCallback, useMemo} from 'react';
import {Button, Modal} from '@panda-design/components';
import {Flex} from 'antd';
import {last} from 'lodash';
import {
    useMCPMessages,
    useMCPChatStatus,
    clearMCPMessages,
    setMCPConversationId,
    useMCPChat,
    useMCPMessage,
    useMCPConversationId,
} from '@/regions/mcp/mcpPlaygroundChat';
import {IconDelete} from '@/icons/mcp';
import {
    ConversationIdProvider,
} from '@/components/Chat/Provider/ConversationIdProvider';
import {APP_IS_ONLINE_PRODUCTION} from '@/constants/app';
import MessagePanel, {MessagePanelRef} from './MessagePanel';
import ChatMessageInput from './ChatMessageInput';
import {useChatMessage} from './hooks/useChatMessage';
import {AgentIdProvider} from './AgentIdProvider';
import WelcomeScreen from './WelcomeScreen';
import ChatLayout from './Layout';

const MCP_PLAYGROUND_AGENT_ID = APP_IS_ONLINE_PRODUCTION ? 13 : 17;

interface Props {
    disabledReason: string;
}
const ChatArea = ({disabledReason: propDisabledReason}: Props) => {
    const conversationId = useMCPConversationId();
    const messages = useMCPMessages();
    const status = useMCPChatStatus();
    const messagePanelRef = useRef<MessagePanelRef>(null);
    const {messageIds} = useMCPChat();
    const lastMessageId =
        messageIds && messageIds.length > 0 ? last(messageIds) : null;
    const lastMessage = useMCPMessage(lastMessageId);
    const lastElementContent = useMemo(
        () => {
            const elements = lastMessage?.elements;
            if (!elements || elements.length === 0) {
                return null;
            }
            return JSON.stringify(last(elements));
        },
        [lastMessage?.elements]
    );

    const handleScrollToBottom = useCallback(
        () => {
            messagePanelRef.current?.scrollToBottom();
        },
        []
    );
    useEffect(
        () => {
            if (lastElementContent) {
                handleScrollToBottom();
            }
        },
        [lastElementContent, handleScrollToBottom]
    );

    const {sendMessage, stopGeneration} =
        useChatMessage(handleScrollToBottom);

    const handleClearMessages = useCallback(
        () => {
            Modal.confirm({
                title: '清空对话',
                content: '确定要清空所有对话记录吗？此操作不可撤销。',
                onOk: () => {
                    clearMCPMessages();
                    setMCPConversationId('');
                    stopGeneration();
                },
            });
        },
        [stopGeneration]
    );

    const isDisabled = status === 'loading';

    const getDisabledReason = () => {
        if (propDisabledReason) {
            return propDisabledReason;
        }
        if (status === 'loading') {
            return '正在处理消息...';
        }
        if (status === 'error') {
            return '发生错误，请稍后重试';
        }
        return '';
    };

    return (
        <AgentIdProvider agentId={MCP_PLAYGROUND_AGENT_ID}>
            <ConversationIdProvider conversationId={conversationId}>
                <ChatLayout
                    hasMessages={!!lastMessage}
                    messagePanel={
                        lastMessage ? (
                            <MessagePanel ref={messagePanelRef} show />
                        ) : <WelcomeScreen />
                    }
                    inputPanel={(
                        <Flex vertical gap={4} style={{width: '100%'}}>
                            {messages.length > 0 && (
                                <Flex justify="flex-end">
                                    <Button
                                        icon={<IconDelete />}
                                        style={{
                                            border: 'none',
                                            padding: '0',
                                            backgroundColor: 'transparent',
                                        }}
                                        onClick={handleClearMessages}
                                    >
                                        清空对话
                                    </Button>
                                </Flex>
                            )}
                            <ChatMessageInput
                                disabled={!!propDisabledReason || isDisabled}
                                disabledReason={getDisabledReason()}
                                onSend={sendMessage}
                                onStop={stopGeneration}
                                isGenerating={status === 'loading'}
                                placeholder="请输入您的问题，可通过cmd+回车换行"
                            />
                        </Flex>
                    )}
                />
            </ConversationIdProvider>
        </AgentIdProvider>
    );
};

export default ChatArea;
