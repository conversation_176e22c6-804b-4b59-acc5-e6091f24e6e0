import {useCallback, useRef, useEffect} from 'react';
import {message} from '@panda-design/components';
import {
    setMCPChatStatus,
    getMCPConversationId,
    setMCPConversationId,
} from '@/regions/mcp/mcpPlaygroundChat';
import {apiPostMcpPlaygroundQuery} from '@/api/mcp';
import {createMcpWebsocket} from '@/utils/mcp/createWebsocket';

export const useChatMessage = (onScrollToBottom?: () => void) => {
    const wsRef = useRef<WebSocket | null>(null);

    const sendMessage = useCallback(
        // eslint-disable-next-line complexity
        async (content: string) => {
            if (!content.trim()) {
                return;
            }

            setMCPChatStatus('loading');

            try {

                const currentConversationId = getMCPConversationId();
                const response = await apiPostMcpPlaygroundQuery({
                    query: content.trim(),
                    conversationId: currentConversationId ?? undefined,
                });

                if (
                    response.conversationId
                    && response.conversationId !== currentConversationId
                ) {
                    setMCPConversationId(response.conversationId);
                    if (wsRef.current) {
                        wsRef.current.close();
                    }

                    const ws = createMcpWebsocket(response.conversationId);
                    if (ws) {
                        wsRef.current = ws;
                    } else {
                        throw new Error('WebSocket 连接创建失败');
                    }
                }
                onScrollToBottom?.();
            } catch (error) {
                message.error(`发送消息失败：${error.response?.data?.msg || error.message}`);
                setMCPChatStatus('error');
            }
        },
        [onScrollToBottom]
    );

    useEffect(
        () => {
            return () => {
                if (wsRef.current) {
                    wsRef.current.close();
                }
            };
        },
        []
    );

    const stopGeneration = useCallback(
        () => {
            if (wsRef.current) {
                wsRef.current.close();
                wsRef.current = null;
                setMCPChatStatus('success');
            }
        },
        []
    );

    return {
        sendMessage,
        stopGeneration,
    };
};
