import {Flex, Form, Input, Tooltip} from 'antd';
import {Button, Modal} from '@panda-design/components';
import {useBoolean} from 'huse';
import {ChangeEvent} from 'react';
import {IconExitFullscreen, IconFullscreen} from '@/icons/mcp';


export default function SystemPromptField() {
    const form = Form.useFormInstance();
    const systemPrompt = Form.useWatch('systemPrompt');

    const [open, {on, off}] = useBoolean(false);
    const handleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
        form.setFieldValue('systemPrompt', e.target.value);
    };

    const handleCancel = () => {
        off();
        form.submit();
    };

    return (
        <>
            <Form.Item
                label={
                    <Flex align="center" justify="space-between" style={{width: '100%', marginRight: -10}}>
                        <Flex align="center" gap={4}>
                            系统提示词
                        </Flex>
                        <Tooltip title="全屏">
                            <Button icon={<IconFullscreen />} type="text" onClick={on} />
                        </Tooltip>
                    </Flex>
                }
                name="systemPrompt"
                validateTrigger={['onChange', 'onBlur']}
            >
                <Input.TextArea
                    placeholder="请输入系统提示词，告诉模型选择工具的规则或条件"
                    rows={4}
                    onBlur={form.submit}
                />
            </Form.Item>
            <Modal
                title="系统提示词"
                footer={null}
                closeIcon={(
                    <Tooltip title="取消全屏">
                        <IconExitFullscreen />
                    </Tooltip>
                )}
                open={open}
                onCancel={handleCancel}
                width="100vw"
                height="100vh"
                styles={{
                    content: {height: '100vh'},
                    mask: {background: 'transparent'},
                }}
                style={{maxWidth: '100vw', overflow: 'hidden'}}
            >
                <Input.TextArea
                    placeholder="请输入系统提示词，告诉模型选择工具的规则或条件"
                    value={systemPrompt}
                    onChange={handleChange}
                    style={{height: 'calc(100vh - 88px)'}}
                />
            </Modal>
        </>
    );
}
