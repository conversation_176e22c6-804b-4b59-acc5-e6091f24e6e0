import {Flex, TableProps, Typography} from 'antd';
import {useCallback, useMemo, useState} from 'react';
import {useRequestCallback} from 'huse';
import {message, Modal} from '@panda-design/components';
import MCPServerTable from '@/components/MCP/MCPServerTable';
import {apiGetSquareServerList} from '@/api/mcp';
import {MCPServerBase} from '@/types/mcp/mcp';
import {apiGetManyExampleMCPServer} from '@/api/mcp/playground';
import ServerListTabs, {MCPServerListType, useServerListTabs} from './ServerListTabs';
import {generateServerListApiParams, getDataSource} from './utils';
import Footer, {ExampleAlert} from './Footer';
import {useInitServerList} from './hooks';
import {getMCPServerAtom} from './region';

interface Props {
    open: boolean;
    onClose: () => void;
    onOk?: (selectedServers: MCPServerBase[]) => void;
    initialValue?: number[];
    type?: MCPServerListType;
}
export default function MCPServerListModal({open, onClose, onOk, initialValue, type = 'square'}: Props) {
    const [current, setCurrent] = useState<number>(1);
    const [labels, setLabels] = useState<number[]>();
    const resetCurrent = useCallback(
        () => setCurrent(1),
        []
    );
    const {
        tabsKey,
        keywords,
        onlySeleted,
        handleValuesChange,
        resetValues,
        form,
    } = useServerListTabs({afterChangeValues: resetCurrent, type, open});

    const handleChange: TableProps<MCPServerBase>['onChange'] = (pagination, filters) => {
        setCurrent(pagination.current);
        setLabels(filters.labels as number[]);
    };

    const [request, {data}] = useRequestCallback(
        apiGetSquareServerList,
        generateServerListApiParams({keywords, labels})
    );
    const [requestExamples, {data: exampleServers}] = useRequestCallback(apiGetManyExampleMCPServer, undefined);
    const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>();

    const dataSource = useMemo(
        () => getDataSource({
            onlySeleted,
            tabsKey,
            squareServers: data?.records || [],
            exampleServers: exampleServers || [],
            selectedRowKeys,
        }),
        [data, exampleServers, onlySeleted, selectedRowKeys, tabsKey]
    );

    const pagination = useMemo(
        () => ({
            total: onlySeleted ? selectedRowKeys?.length
                : tabsKey === 'square' ? data?.total : exampleServers?.length,
            current,
        }),
        [tabsKey, data, exampleServers, current, onlySeleted, selectedRowKeys]
    );

    const handleCancel = useCallback(
        () => {
            onClose();
            resetValues();
            setSelectedRowKeys([]);
            setLabels([]);
        },
        [onClose, resetValues]
    );

    const handleOk = useCallback(
        () => {
            if (selectedRowKeys.length > 10) {
                message.error('最多只能选择10个MCP Server');
                return;
            }
            onOk?.(selectedRowKeys.map(id => getMCPServerAtom(id)));
            handleCancel();
        },
        [handleCancel, onOk, selectedRowKeys]
    );

    useInitServerList({
        open,
        request,
        requestExamples,
        initialValue,
        type,
        exampleServers,
        setSelectedRowKeys,
        servers: data?.records,
    });

    return (
        <Modal
            title={(
                <Flex align="baseline" gap={8}>
                    连接MCP Servers
                    <Typography.Text type="secondary" style={{fontWeight: 400}}>
                        当前仅支持SSE类型的MCP Server试用
                    </Typography.Text>
                </Flex>
            )}
            width={1000}
            open={open}
            onCancel={handleCancel}
            footer={(
                <Footer
                    onCancel={handleCancel}
                    onOk={handleOk}
                    selectedCount={selectedRowKeys?.length}
                />
            )}
            destroyOnHidden
            maskClosable={false}
        >
            <ServerListTabs form={form} onValuesChange={handleValuesChange} />
            <ExampleAlert type={tabsKey} />
            <MCPServerTable
                selectedRowKeys={selectedRowKeys}
                onSelect={setSelectedRowKeys}
                dataSource={dataSource}
                pagination={pagination}
                onChange={handleChange}
                scroll={{y: '40vh'}}
            />
        </Modal>
    );
}
