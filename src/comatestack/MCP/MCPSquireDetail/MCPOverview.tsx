import styled from '@emotion/styled';
import {Flex, Typography} from 'antd';
import {useMemo} from 'react';
import {Markdown} from '@/design/Markdown';
import {useMCPServerId} from '@/components/MCP/hooks';
import {useMCPServer} from '@/regions/mcp/mcpServer';
import <PERSON><PERSON><PERSON><PERSON>ie<PERSON> from '@/components/MCP/JSONViewer';

const Container = styled(Flex)`
    flex: 1;
    display: grid;
    grid-template-columns: 6fr 4fr;
    gap: 16px;
`;

const Content = styled.div`
    border-radius: 6px;
    border: 1px solid #E8E8E8;
    padding: 16px 24px;
    flex: 1;
    overflow: auto;
    ::-webkit-scrollbar {
        display: none;
    }
`;


const MCPOverview = () => {

    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);

    const config = useMemo(
        () => {
            if (mcpServer?.serverConf?.serverConfig) {
                try {
                    return JSON.parse(mcpServer?.serverConf?.serverConfig);
                } catch (e) {
                    return mcpServer?.serverConf?.serverConfig;
                }
                // return JSON.parse(mcpServer?.serverConf?.serverConfig);
            }
        },
        [mcpServer?.serverConf?.serverConfig]
    );
    return (
        <Container>
            <Content>
                <Markdown content={mcpServer?.serverConf?.overview || ''} codeHighlight />
            </Content>
            <Content>
                <Flex vertical gap={12}>
                    <Typography.Title level={4}>Server Config</Typography.Title>
                    <JSONViewer json={config} />
                </Flex>
            </Content>
        </Container>
    );
};

export default MCPOverview;
