/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Button, Modal, message} from '@panda-design/components';
import {Flex, Form, Space, Tooltip} from 'antd';
import {useCallback, useState} from 'react';
import {useBoolean} from 'huse';
import {useSearchParams} from '@panda-design/router';
import {IconAlert, IconRefresh} from '@/icons/mcp';
import {IconSuccess} from '@/icons/status';
import {useMCPServer, useMCPServerTools} from '@/regions/mcp/mcpServer';
import {useMCPServerId} from '@/components/MCP/hooks';
import {MCPToolDebugModalButton} from '@/components/MCP/MCPToolDebug/MCPToolDebugModalButton';
import {useMCPToolDebugContext} from '@/components/MCP/MCPToolDebug/Providers/MCPToolDebugProvider';
import {useTouchedBasePath} from '../regions';
import {useActiveTool, useHandleSaveTool} from './hooks';
import {useToolParamsConfigContext} from './Provider/toolParamsConfigProvider';

const Wrapper = styled(Flex)`
    padding: 14px 24px;
`;

const StyledButton = styled(Button)`
    color: #317ff5 !important;
    border: 1px solid #317ff5 !important;
    &:hover{
        color: #317ff5 !important;
        border: 1px solid #317ff5 !important;
    }
`;

const Title = styled.h3`
    font-size: 18px;
    line-height: 28px;
    font-weight: 500;
    margin: 0;
    svg {
        margin-right: 4px;
    }
`;

const XLine = styled.div`
    height: 1px;
    width: 100%;
    background-color: #E8E8E8;
`;

const ToolActions = () => {
    const form = Form.useFormInstance();
    const {activeTab} = useSearchParams();
    const touchedBasePath = useTouchedBasePath();
    const [formUpdated, setFormUpdated] = useState(false);
    const {refreshToolParams} = useMCPToolDebugContext();
    const [loading, {on, off}] = useBoolean();
    const serverSourceType = Form.useWatch('serverSourceType');
    const mcpServerId = useMCPServerId();
    const mcpServerTools = useMCPServerTools(mcpServerId);
    const mcpServer = useMCPServer(mcpServerId);
    const {setFieldValue} = Form.useFormInstance();
    const {toolParamsChanged} = useToolParamsConfigContext();
    // 这个要从实时数据里取，不然不能及时反映状态的变化
    const mcpServerToolsInForm = Form.useWatch('tools');
    const {activeToolIndex} = useActiveTool();
    const currentToolStatus = mcpServerToolsInForm?.[activeToolIndex]?.toolStatus;
    const onSuccess = useCallback(
        () => {
            setFormUpdated(true);
            toolParamsChanged.current = false;
        },
        [toolParamsChanged]
    );
    const handleSave = useHandleSaveTool({on, off, onSuccess});
    const isFormDirty = useCallback(
        async () => {
            if (toolParamsChanged.current) {
                return true;
            }
            try {
                await form.validateFields();
                return Boolean(touchedBasePath?.[activeTab]);
            } catch {
                return true;
            }
        },
        [form, touchedBasePath, activeTab, toolParamsChanged]
    );

    const handleReset = useCallback(
        () => {
            Modal.confirm({
                content: '重置后，工具中的配置信息将会恢复成初始状态。确定对工具进行重置吗？',
                icon: <IconAlert />,
                onOk: () => {
                    if (mcpServerTools) {
                        setFieldValue('tools', mcpServerTools);
                        setFieldValue('activeToolIndex', 0);
                    }
                },
            });
        },
        [mcpServerTools, setFieldValue]
    );

    const saveWidthConfirm = () => {
        if (mcpServer?.serverStatus === 'release') {
            Modal.confirm({
                content: '当前MCP Server已发布，点击保存该修改会立马生效，对订阅的应用产生影响，请确认是否继续保存',
                icon: <IconAlert />,
                okText: '保存',
                cancelText: '取消',
                onOk: () => {
                    handleSave();
                    setFormUpdated(true);
                },
            });
        } else {
            handleSave();
            setFormUpdated(true);
        }
    };

    const debugBlocker = useCallback(
        async () => {
            const values = form.getFieldsValue(true);
            window.console.log(values);
            const isDirty = await isFormDirty();
            if (isDirty) {
                message.info('表单内容未保存，请先保存表单内容再调试工具');
                return true;
            } else if (formUpdated) {
                refreshToolParams();
            }
        },
        [isFormDirty, formUpdated, refreshToolParams, form]
    );
    /* const debug = useCallback(
        () => {

        },
        []
    ); */

    return (
        <div>
            <Wrapper justify="space-between" align="center">
                <Title>
                    <IconSuccess fill="#00CC6D" />
                    试用场景信息
                </Title>
                <Space>
                    {serverSourceType !== 'script' && (
                        <>
                            <Button
                                disabled={false}
                                icon={<IconRefresh />}
                                onClick={handleReset}
                                type="text"
                            >
                                重置
                            </Button>
                            {/* <Button icon={<IconTool />} type="text" onClick={debug}>调试</Button> */}
                        </>
                    )
                    }
                    {
                        mcpServer?.serverProtocolType === 'SSE'
                        && (
                            <Tooltip title={currentToolStatus !== 'complete' ? '请先完善工具配置' : undefined}>
                                <MCPToolDebugModalButton
                                    blocker={debugBlocker}
                                    disabled={currentToolStatus !== 'complete'}
                                />
                            </Tooltip>
                        )
                    }
                    {(
                        <StyledButton
                            disabled={false}
                            type="text"
                            tooltip={<Space><IconAlert style={{color: '#F58300'}} />工具发生修改后，请及时保存～</Space>}
                            onClick={saveWidthConfirm}
                            loading={loading}
                        >
                            保存工具
                        </StyledButton>
                    )}
                </Space>
            </Wrapper>
            <XLine />
        </div>
    );
};

export default ToolActions;

