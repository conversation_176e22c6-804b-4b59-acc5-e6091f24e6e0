import type { SVGProps } from "react";
const SvgDebug = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <path fill="#000" d="M0 0h16v16H0z" opacity={0.01} />
        <path
            fill="#000"
            d="M13.027 1.75c.676 0 1.223.504 1.223 1.124v10.252c0 .62-.547 1.124-1.223 1.124H3.448c-.938 0-1.698-.699-1.698-1.56V2.874c0-.62.547-1.124 1.223-1.124zM3.585 2.998c-.263 0-.476.196-.476.438v9.253c0 .171.15.31.334.313h8.972c.263 0 .476-.196.476-.437v-9.13c0-.24-.213-.437-.476-.437zm6.376 5.19a1.79 1.79 0 1 1-1.674 2.421H4.25V9.36h4.031a1.79 1.79 0 0 1 1.68-1.171m0 1.25a.54.54 0 1 0 0 1.078.54.54 0 0 0 0-1.078M6.039 4.25a1.79 1.79 0 0 1 1.68 1.172h4.031v1.25H7.713A1.79 1.79 0 1 1 6.039 4.25m0 1.25a.54.54 0 1 0 0 1.078.54.54 0 0 0 0-1.078"
        />
    </svg>
);
export default SvgDebug;
