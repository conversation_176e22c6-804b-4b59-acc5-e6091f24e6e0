/**
 * @file 搜索框，可根据资源账户、虚拟机名、地域或IP进行机器筛选
 */
import {Input, Select, Space} from 'antd';
import {useCallback, useState} from 'react';
import {useBoolean} from 'huse';
import {partial} from 'lodash';
import {width} from '@panda-design/components';
import LuoshuAccountPicker from '@/third-party/etui/LuoshuAccountPicker';

const {Option} = Select;
const {Search} = Input;

interface Props {
    accountId: string;
    setAccountId: (value: string) => void;
    onMachineNameChange: (value: string) => void;
    onClusterChange: (value: string) => void;
    onIpChange: (value: string) => void;
    fetchMachineRecords: () => void;
}

const MachineFilter = ({
    accountId,
    setAccountId,
    onMachineNameChange,
    onClusterChange,
    onIpChange,
    fetchMachineRecords,
}: Props) => {
    const [selectCategory, setSelectCategory] = useState('资源账户');
    const [hasSearched, {on: startSearch, off: endSearch}] = useBoolean(false);
    const [searchValue, setSearchValue] = useState('');

    const onSelectChange = (value: string) => {
        setSelectCategory(value);
        setAccountId('');
        onMachineNameChange('');
        onClusterChange('');
        onIpChange('');
        setSearchValue('');
        if (hasSearched) {
            fetchMachineRecords();
            endSearch();
        }
    };

    const changeSearchContent = useCallback(
        (value: string) => {
            if (selectCategory === '资源账户') {
                startSearch();
            }
            else if (selectCategory === '虚拟机') {
                startSearch();
                onMachineNameChange(value);
            }
            else if (selectCategory === '地域') {
                startSearch();
                onClusterChange(value);
            }
            else if (selectCategory === 'IP') {
                startSearch();
                onIpChange(value);
            }
        },
        [onClusterChange, onIpChange, onMachineNameChange, selectCategory, startSearch]
    );

    return (
        <Space.Compact>
            <Select
                defaultValue="资源账户"
                onChange={onSelectChange}
                style={{width: 100}}
            >
                <Option value="资源账户">资源账户</Option>
                <Option value="虚拟机">虚拟机</Option>
                <Option value="IP">IP</Option>
            </Select>
            {selectCategory === '资源账户' && (
                <LuoshuAccountPicker
                    allowClear
                    value={accountId}
                    className={width(340)}
                    onChange={id => setAccountId(id)}
                    onClear={partial(setAccountId, '')}
                />
            )}
            {selectCategory !== '资源账户' && (
                <Search
                    value={searchValue}
                    style={{width: '80%'}}
                    placeholder="请输入"
                    onChange={e => setSearchValue(e.target.value)}
                    onSearch={changeSearchContent}
                    enterButton
                    allowClear
                />
            )}
        </Space.Compact>
    );
};

export default MachineFilter;
