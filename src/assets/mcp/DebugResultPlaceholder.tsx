import type { SVGProps } from "react";
const SvgDebugResultPlaceholder = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={240}
        height={206}
        fill="none"
        {...props}
    >
        <path
            fill="#BFBFBF"
            d="M192.155 11v37.36h-65.647V11zm-63.647 2v33.36h61.647V13z"
        />
        <path
            fill="url(#debugResultPlaceholder_svg__a)"
            d="M141.652 31.448h7.072v14.144h-7.072z"
        />
        <path
            fill="url(#debugResultPlaceholder_svg__b)"
            d="M169.939 31.448h7.072v14.144h-7.072z"
        />
        <path
            fill="url(#debugResultPlaceholder_svg__c)"
            d="M155.795 20.84h7.072v24.751h-7.072z"
        />
        <path
            fill="#BFBFBF"
            d="M212.53 57.083v44h-37.531v-2h35.531v-40h-42.198v24.54h-2v-26.54z"
        />
        <rect
            width={32.707}
            height={7.072}
            x={172.635}
            y={75.95}
            fill="url(#debugResultPlaceholder_svg__d)"
            rx={1}
        />
        <rect
            width={32.707}
            height={7.072}
            x={172.635}
            y={65.343}
            fill="url(#debugResultPlaceholder_svg__e)"
            rx={1}
        />
        <path
            fill="#2E2E2E"
            d="M113.182 24.401v2h-91a2 2 0 0 0-1.994 1.851l-.006.15V123.4h-2v-95a4 4 0 0 1 3.8-3.995l.2-.005z"
        />
        <path
            fill="#C4C3C4"
            d="M27.87 31.735a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3m5.304 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3m5.304 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3"
        />
        <path
            fill="#2E2E2E"
            d="m91.779 84.006.226 1.987c-28.44 3.24-42.718 21.127-43 54.006L80 140v-11h2v13H47v-1c0-34.138 14.685-53.216 43.889-56.887z"
        />
        <path
            fill="#BFBFBF"
            d="M128.801 88.541c9.906 3.632 17.081 9.498 21.481 17.585l.255.478-1.77.93c-4.054-7.723-10.736-13.353-20.09-16.905l-.565-.21z"
        />
        <path
            stroke="#2E2E2E"
            strokeWidth={2}
            d="m130.84 75.09-4.86 12.58-3.107-2.4s-3.529 4.284-6.075 7.384c-4.712-.297-8.526-6.64-8.526-7.54"
        />
        <path
            fill="#2E2E2E"
            fillRule="evenodd"
            d="M87.533 82.209c0 11.845 18.846 14.845 23.001 4.709 4.154-10.137.554-16.59 3.527-18.179s13.191 8.072 17.782 7.025 2.677-11.212-1.634-16.05c-4.312-4.837-15.611-8.47-24.703 1.509-9.093 9.98-17.973 9.14-17.973 20.986"
            clipRule="evenodd"
        />
        <path
            fill="#2E2E2E"
            fillRule="evenodd"
            d="m107.733 83.927-4.035 5.981 13.485 12.75v-9.732s-2.615-.603-4.578-1.886-4.872-7.113-4.872-7.113"
            clipRule="evenodd"
        />
        <path
            fill="#2E2E2E"
            d="M82 140h44.481l-3.649 9.055-1.082.516-1.451.684q-.36.169-.719.335l-1.426.656-.707.321-1.401.628-1.385.61-1.368.591-.678.288-1.344.563-.666.274-1.319.535-.653.26-1.294.506-1.278.488-.633.237-1.254.46q-.622.225-1.237.441l-1.22.423-1.205.403q-.597.198-1.187.385l-1.172.366-1.155.348-1.139.329-.563.157-1.114.301-1.098.281q-.818.205-1.616.388l-1.057.235c-8.565 1.842-15.496 1.805-20.807-.124-10.686-3.881-16.594-10.383-17.56-19.432l-.04-.419 1.992-.178c.751 8.432 6.124 14.456 16.291 18.149 4.989 1.812 11.65 1.813 19.971-.009l1.027-.233.52-.123 1.052-.259.532-.137 1.077-.287 1.094-.305q.55-.157 1.11-.324l1.126-.342 1.143-.36 1.16-.378q.292-.096.586-.196l1.185-.406 1.201-.424 1.217-.442.615-.228 1.242-.47 1.259-.488 1.276-.506.643-.26 1.301-.534 1.316-.552 1.333-.57 1.35-.589.681-.302 1.374-.616.693-.315 1.399-.644 1.416-.662 1.08-.513 2.252-5.586H82z"
        />
        <path
            fill="#2E2E2E"
            fillRule="evenodd"
            d="M82 141v6l-13-6z"
            clipRule="evenodd"
        />
        <path
            fill="#2E2E2E"
            d="M145.554 125.93h70.766l-13.482 31.383h-70.766z"
            opacity={0.9}
        />
        <path
            fill="url(#debugResultPlaceholder_svg__f)"
            d="M202.813 122h-59.577l-10.301 24.32h-5.294L142.827 110h65.334z"
        />
        <path
            fill="#545454"
            d="M85.014 193.616v.826c-.658.056-1.344.126-2.058.196v1.078h-.924v-1.008c-.924.056-1.904.126-2.912.182l-.126-.896c1.064-.056 2.086-.098 3.038-.154v-1.162h-2.604l-.224-.798c.392-.448.77-.98 1.106-1.61h-1.498v-.896h1.946c.126-.308.252-.616.364-.924l1.008.168c-.098.252-.196.504-.308.756h3.416v.896h-3.836c-.308.588-.644 1.12-.994 1.582h1.624v-.924h.924v.924h2.184v.826h-2.184v1.106q1.092-.084 2.058-.168m1.96-.924q-.231 1.764-1.134 3.066l-.7-.728c.616-.91.952-1.988.98-3.248v-2.198c1.736-.168 3.108-.476 4.088-.924l.644.756c-1.036.448-2.296.77-3.808.938v1.456h4.2v.882H89.76v3.038h-.966v-3.038zm2.702 3.444v5.264H88.64v-.546h-7.112v.546h-1.036v-5.264zm-8.148 3.85h7.112v-1.092h-7.112zm0-1.904h7.112v-1.092h-7.112zm12.334-8.764h10.052v.98h-5.012v.938a26 26 0 0 1-.182 2.45h6.328v.966h-4.34v4.886c0 .448.224.686.672.686h1.778c.336 0 .588-.098.742-.28.196-.224.322-.868.392-1.96l.952.308c-.112 1.372-.322 2.198-.63 2.506-.266.252-.686.378-1.274.378h-2.268c-.924 0-1.372-.476-1.372-1.428v-5.096h-1.134c-.224 1.218-.574 2.268-1.05 3.164-.868 1.512-2.268 2.702-4.214 3.542l-.56-.896c1.932-.868 3.262-2.002 4.018-3.43.336-.7.588-1.498.77-2.38h-4.606v-.966h4.774c.112-.756.168-1.582.182-2.45v-.938h-4.018zm13.86-.616c.952.742 1.764 1.47 2.422 2.198l-.714.714c-.588-.7-1.386-1.442-2.408-2.254zm8.176 12.446c-.756 0-1.568-.014-2.45-.028s-1.61-.098-2.17-.252c-.532-.168-1.022-.518-1.456-1.022-.182-.238-.364-.35-.532-.35q-.504 0-1.68 1.932l-.756-.686c.756-1.176 1.414-1.89 2.002-2.142v-4.256h-2.002v-.924h2.94v5.25c.112.084.21.182.322.308.336.406.686.7 1.078.882.42.196 1.05.308 1.862.336.728.014 1.624.028 2.702.028.672 0 1.358-.014 2.058-.028a34 34 0 0 0 1.582-.056l-.252 1.008zm-4.186-11.788h6.426v.924h-6.426zm-.854 3.178h8.302v.966h-4.368c-.77 1.988-1.456 3.402-2.072 4.242 1.82-.196 3.318-.448 4.508-.728a24 24 0 0 0-1.12-2.268l.826-.266q1.134 1.932 2.016 4.41l-.896.238c-.14-.434-.294-.854-.462-1.246-1.54.364-3.556.658-6.048.896l-.252-.882c.588-.168 1.414-1.638 2.506-4.396h-2.94zm15.092-3.08h6.706v.994h-6.706zm-.378 3.556h7.7v.994h-2.268v6.118c0 .812-.462 1.218-1.358 1.218h-2.058l-.21-.994c.672.042 1.302.07 1.918.07.448 0 .686-.182.686-.546v-5.866h-4.41zm-1.834-4.326.91.42q-1.176 2.331-3.78 4.116l-.294-1.008c1.456-1.036 2.506-2.212 3.164-3.528m.658 3.192.91.42c-.406.784-.91 1.54-1.526 2.24v6.86h-.994v-5.838a18 18 0 0 1-1.834 1.456l-.294-1.008c1.708-1.204 2.954-2.576 3.738-4.13m15.372 6.72v.952c-1.428.518-3.038.938-4.858 1.274l-.126-.98c1.904-.308 3.556-.728 4.984-1.246m-2.59-10.08.938.364c-.714 1.82-1.442 3.276-2.17 4.368a53 53 0 0 0 1.988-.294c.266-.504.546-1.022.826-1.554l.882.322c-1.274 2.366-2.338 4.046-3.192 5.054a25 25 0 0 0 3.15-.84v.896q-2.268.777-4.368 1.05l-.266-.91q.273-.126.462-.294c.532-.546 1.19-1.47 1.974-2.786-.798.126-1.596.238-2.408.35l-.252-.924q.231-.105.42-.378c.784-1.288 1.456-2.758 2.016-4.424m2.422 1.778h3.346v-1.694h1.022v1.694h3.332v.98h-3.332v2.072h2.926v.98h-6.832v-.98h2.884v-2.072H139.6zm6.888 5.432v5.628h-.98v-.672h-4.004v.672h-.994v-5.628zm-4.984 4.004h4.004v-3.052h-4.004zm8.792-10.612h9.338v5.418h-4.116v1.274h5.572v.952h-4.676c.994 1.26 2.66 2.394 4.984 3.374l-.686.882c-2.436-1.274-4.144-2.688-5.124-4.256h-.07v4.62h-1.036v-4.62h-.056c-1.064 1.722-2.8 3.136-5.236 4.242l-.574-.924q3.423-1.302 4.956-3.318h-4.662v-.952h5.572v-1.274h-4.186zm8.344 4.536V192.3h-3.122v1.358zm-4.158 0V192.3h-3.178v1.358zm-3.178-2.24h3.178v-1.4h-3.178zm4.214-1.4v1.4h3.122v-1.4z"
        />
        <defs>
            <linearGradient
                id="debugResultPlaceholder_svg__a"
                x1={148.724}
                x2={148.724}
                y1={45.591}
                y2={31.448}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" />
                <stop offset={1} stopColor="#F2F2F2" />
            </linearGradient>
            <linearGradient
                id="debugResultPlaceholder_svg__b"
                x1={177.011}
                x2={177.011}
                y1={45.591}
                y2={31.448}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" />
                <stop offset={1} stopColor="#F2F2F2" />
            </linearGradient>
            <linearGradient
                id="debugResultPlaceholder_svg__c"
                x1={162.867}
                x2={162.867}
                y1={45.591}
                y2={20.84}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" />
                <stop offset={1} stopColor="#F2F2F2" />
            </linearGradient>
            <linearGradient
                id="debugResultPlaceholder_svg__d"
                x1={201.433}
                x2={172.635}
                y1={76.373}
                y2={76.373}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" />
                <stop offset={1} stopColor="#F2F2F2" />
            </linearGradient>
            <linearGradient
                id="debugResultPlaceholder_svg__e"
                x1={201.433}
                x2={172.635}
                y1={65.765}
                y2={65.765}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" />
                <stop offset={1} stopColor="#F2F2F2" />
            </linearGradient>
            <linearGradient
                id="debugResultPlaceholder_svg__f"
                x1={167.901}
                x2={140.674}
                y1={91.84}
                y2={152.199}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#32C5FF" stopOpacity={0.466} />
                <stop offset={1} stopColor="#B620E0" />
            </linearGradient>
        </defs>
    </svg>
);
export default SvgDebugResultPlaceholder;
