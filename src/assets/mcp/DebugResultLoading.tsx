import type { SVGProps } from "react";
const SvgDebugResultLoading = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={240}
        height={206}
        fill="none"
        {...props}
    >
        <path
            fill="#fff"
            d="m118.119 46.228 1.002 9.239s2.888-2.31 4.461 0c.324 3.909-3.422 5.288-3.452 5.3l-.506 6.502c52.399 15.124 53.742 42.812 53.744 42.856l-3.294.541v1.127c-1.652 23.624-14.327 27.072-14.402 27.093H65.925c-.046-.087-14.953-28.196-12.563-46.77C55.944 73.154 88.42 70.011 88.535 70l16.846-3.07c-10.032.714-8.205-9.751-8.205-9.751l1.792-8.384 1.015-5.83 5.666-4.465zM191 18c12.703 0 23 10.298 23 23s-10.297 23-23 23-23-10.297-23-23 10.298-23 23-23"
        />
        <circle cx={188} cy={36} r={23} fill="#BFBFBF" />
        <circle cx={191} cy={41} r={23} stroke="#000" strokeWidth={2} />
        <path
            stroke="#fff"
            strokeWidth={2}
            d="M190.721 22.408V39l-7.064-6.16"
        />
        <g clipPath="url(#debugResultLoading_svg__a)">
            <path stroke="#000" strokeWidth={2} d="M25 139h180" />
            <path
                fill="#8F8F8F"
                d="M26.008 149.004h1.134v9.002h5.782V159h-6.916zm11.325 2.562c1.078 0 1.946.364 2.59 1.12.615.714.923 1.61.923 2.702 0 1.078-.307 1.974-.91 2.674q-.987 1.134-2.603 1.134c-1.093 0-1.947-.378-2.59-1.134-.617-.7-.925-1.596-.925-2.674 0-1.092.309-1.988.925-2.702.643-.756 1.497-1.12 2.59-1.12m0 .938c-.77 0-1.372.294-1.792.882-.392.532-.574 1.19-.574 2.002 0 .798.181 1.456.574 1.988.42.588 1.022.882 1.792.882q1.134 0 1.806-.882c.377-.532.573-1.204.573-1.988 0-.812-.196-1.47-.574-2.002q-.672-.882-1.805-.882m8.119-.938c1.036 0 1.792.266 2.296.812.42.476.644 1.134.644 1.974V159h-1.05v-1.218c-.28.392-.658.714-1.106.966a3.7 3.7 0 0 1-1.806.448c-.728 0-1.302-.182-1.736-.546a1.83 1.83 0 0 1-.672-1.456c0-.826.322-1.442.994-1.862.588-.392 1.428-.602 2.52-.616l1.736-.028v-.378c0-1.218-.63-1.82-1.89-1.82-.532 0-.966.098-1.302.322-.378.224-.616.574-.728 1.036l-1.106-.084c.154-.756.532-1.33 1.12-1.694.532-.336 1.218-.504 2.086-.504m1.82 3.962-1.666.028c-1.624.028-2.436.56-2.436 1.61 0 .336.126.602.406.826.28.21.644.322 1.092.322.686 0 1.288-.224 1.82-.672.518-.434.784-.938.784-1.498zm8.198-6.72h1.12V159h-1.05v-1.19c-.504.924-1.316 1.386-2.408 1.386-1.064 0-1.89-.392-2.478-1.148-.546-.7-.812-1.596-.812-2.688 0-1.064.266-1.932.812-2.632.588-.784 1.386-1.162 2.422-1.162s1.834.504 2.394 1.526zm-2.156 3.682c-.798 0-1.4.28-1.792.84-.364.49-.532 1.162-.532 2.03s.168 1.554.518 2.058c.392.56.98.854 1.764.854.728 0 1.288-.28 1.694-.84.35-.504.532-1.162.532-1.974v-.14c0-.84-.21-1.526-.602-2.044a1.9 1.9 0 0 0-1.582-.784m5.935-3.458c.238 0 .434.07.602.238q.252.21.252.588a.81.81 0 0 1-.252.602.86.86 0 0 1-.602.238.86.86 0 0 1-.602-.238.8.8 0 0 1-.238-.602c0-.252.07-.448.238-.588a.8.8 0 0 1 .602-.238m-.56 2.73h1.12V159h-1.12zm6.606-.196c1.792 0 2.702.994 2.702 2.982V159h-1.12v-4.368c0-1.414-.644-2.114-1.904-2.114q-.756 0-1.302.546c-.392.392-.602.91-.644 1.568V159h-1.12v-7.238h1.12v1.064q.378-.609.966-.924a2.6 2.6 0 0 1 1.302-.336m7.414 0c1.092 0 1.904.476 2.436 1.442v-1.246h1.12v6.734c0 2.31-1.106 3.472-3.304 3.472-.98 0-1.736-.196-2.24-.588q-.777-.588-.966-1.764h1.12c.084.49.294.854.616 1.078.322.238.812.364 1.47.364 1.456 0 2.184-.812 2.184-2.408v-1.274c-.532.966-1.344 1.456-2.436 1.456-1.008 0-1.806-.35-2.394-1.036-.588-.672-.868-1.526-.868-2.576 0-1.064.28-1.918.868-2.59.588-.714 1.386-1.064 2.394-1.064m.168.924c-.728 0-1.288.252-1.694.784-.392.476-.588 1.134-.588 1.946 0 .756.168 1.386.518 1.862.392.532.966.812 1.75.812.728 0 1.288-.252 1.708-.756.392-.49.588-1.134.588-1.918 0-.812-.196-1.47-.588-1.946-.42-.532-.98-.784-1.694-.784M87.527 157.334c.252 0 .448.07.616.238.154.154.238.35.238.588a.81.81 0 0 1-.238.602.9.9 0 0 1-.616.238.8.8 0 0 1-.588-.238.81.81 0 0 1-.252-.602q0-.357.252-.588c.154-.168.35-.238.588-.238m4.676 0c.238 0 .448.07.616.238.154.154.238.35.238.588a.81.81 0 0 1-.238.602.9.9 0 0 1-.616.238.86.86 0 0 1-.602-.238.8.8 0 0 1-.238-.602c0-.238.07-.434.238-.588a.8.8 0 0 1 .602-.238m4.662 0c.238 0 .434.07.602.238q.252.231.252.588a.77.77 0 0 1-.252.602.86.86 0 0 1-.602.238.86.86 0 0 1-.602-.238.8.8 0 0 1-.238-.602c0-.238.07-.434.238-.588a.8.8 0 0 1 .602-.238"
            />
            <path
                stroke="#000"
                strokeWidth={2}
                d="M170.074 111c-.99 22.014-8.902 25.834-13.806 27.934h-51.802q10.65-12.576 35.394-11.494l3.091-20.184"
            />
            <path
                fill="#000"
                fillRule="evenodd"
                d="M141.722 114.951 140 125.877s.6-3.828 10.589-8.119c9.988-4.291 20.755-6.758 20.755-6.758z"
                clipRule="evenodd"
            />
            <path
                stroke="#000"
                strokeWidth={2}
                d="M88.536 70s-32.587 3.12-35.174 22.116c-2.394 18.603 12.563 46.77 12.563 46.77"
            />
            <path
                fill="#fff"
                stroke="#000"
                strokeWidth={2}
                d="M85 98.778h8.189v7.351H85z"
            />
            <path
                fill="#fff"
                fillRule="evenodd"
                stroke="#000"
                strokeWidth={2}
                d="M89.094 94.64H116l-3.814 17.153H92.908z"
                clipRule="evenodd"
            />
            <path
                fill="url(#debugResultLoading_svg__b)"
                fillRule="evenodd"
                d="m114.093 103.216-1.907 8.577H92.908L91 103.216s2.8-1.838 5.763-1.838 5.924 1.838 5.924 1.838 3.174 1.838 5.917 1.838c2.744 0 5.488-1.838 5.488-1.838"
                clipRule="evenodd"
            />
            <path
                stroke="#000"
                strokeWidth={2}
                d="M89.094 94.64H116l-3.814 17.153H92.908z"
                clipRule="evenodd"
            />
            <path
                stroke="#000"
                strokeWidth={2}
                d="M101.956 83v4.179l-1.973 2.067v5.448M106.635 85.315v3.09l-1.973 2.066v2.446M94.358 115.793h16.378"
            />
            <path
                fill="#fff"
                fillRule="evenodd"
                d="M82.555 98.545s3.43.933 4.466 2.834c1.522 3.289-2.492 10.543-3.71 11.897-1.025 1.069-6.326 1.784-6.326 1.784"
                clipRule="evenodd"
            />
            <path
                stroke="#000"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M82.555 98.545s3.43.933 4.466 2.834c1.522 3.289-2.492 10.543-3.71 11.897-1.025 1.069-6.326 1.784-6.326 1.784"
            />
            <path
                fill="#fff"
                fillRule="evenodd"
                d="M67.3 138.511V114.61s-.24-14.655 6.889-19.338c1.946-1.194 4.747-.897 6.946-.357 2.102.517 3.66 4.238 1.557 3.727-1.839-.298-6.202 0-6.202 0"
                clipRule="evenodd"
            />
            <path
                stroke="#000"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M67.3 138.511V114.61s-.24-14.655 6.889-19.338c1.946-1.194 4.747-.897 6.946-.357 2.102.517 3.66 4.238 1.557 3.727-1.839-.298-6.202 0-6.202 0"
            />
            <path
                fill="#fff"
                fillRule="evenodd"
                d="M76.595 99.55s1.133-2.004 2.77-2.004.757-.973.757-.973l-5.067-.275-.309 1.248v3.297z"
                clipRule="evenodd"
            />
        </g>
        <path
            fill="#000"
            fillRule="evenodd"
            d="M99.183 48.795s-4.384-.51-3.818-5.04c.5-2.069 2.66-2.806 2.66-2.806s-.953-1.508.674-3.545c1.471-1.345 4.023-1.103 4.023-1.103s-.285-1.657 1.698-2.884 4.869.5 4.869 2.008c0 0 2.312-1.693 3.925-.735s2.18 3.307 2.18 3.307 1.711-1.408 3.7-.593 1.927 3.925 1.927 3.925 4.048 1.546 3.429 4.133-3.429 2.196-3.429 2.196-.897 1.137-2.551 1.137-3.456-1.529-3.076-2.61c0 0-3.577.12-4.197-2.032-.302-1.13-.149-1.848-.149-1.848s-3.998.056-4.816-3.123c0 0-4.144.905-5.027 1.715-.883.811-2.022 7.898-2.022 7.898"
            clipRule="evenodd"
        />
        <path
            stroke="#000"
            strokeWidth={2}
            d="M110.843 65.92c-15.19 4.162-13.895-7.72-13.895-7.72L99.9 44.8s2.816-3.642 4.586-3.904c1.77-.26 3.672 1.122 3.672 1.122 1.255 1.122 1.283 2.834 3.017 2.565 0 0 1.86 4.224 4.704 2.762 0 0 .342 1.448 1.329 1.448 0 0 1.563 9.379 1.957 8.115l.46-1.77s.534-1.844 2.876-.743c2.343 1.1 1.073 5.41-2.413 6.422l-.463 6.452s52.239 15.045 53.745 42.855L141.842 115"
        />
        <path
            fill="#000"
            fillRule="evenodd"
            d="M99.9 45.801q2.806-5.303 4.576-5.564c1.77-.26 3.681 2.378 3.681 2.782s1.283 2.833 3.017 2.565q1.86 4.224 4.704 2.762.342 1.447 1.329 1.447c.233 6.4 2.048 6.361 2.048 6.361l1.23-1.156h1.063s2.449-5.615.693-9.414c-1.756-3.8-14.502-9.283-14.502-9.283l-6.744 2.404-2.4 5.299z"
            clipRule="evenodd"
        />
        <path
            stroke="#000"
            strokeWidth={2}
            d="m106.903 50.788-3.044 6.815 3.044.59"
        />
        <path
            fill="#000"
            fillRule="evenodd"
            d="M103.466 66.464 107.862 70l7.114-6.14z"
            clipRule="evenodd"
        />
        <path
            fill="#545454"
            d="M41.768 198.6v.952c-1.428.518-3.038.938-4.858 1.274l-.126-.98c1.904-.308 3.556-.728 4.984-1.246m-2.59-10.08.938.364c-.714 1.82-1.442 3.276-2.17 4.368a53 53 0 0 0 1.988-.294c.266-.504.546-1.022.826-1.554l.882.322c-1.274 2.366-2.338 4.046-3.192 5.054a25 25 0 0 0 3.15-.84v.896q-2.268.777-4.368 1.05l-.266-.91q.273-.126.462-.294c.532-.546 1.19-1.47 1.974-2.786-.798.126-1.596.238-2.408.35l-.252-.924q.231-.105.42-.378c.784-1.288 1.456-2.758 2.016-4.424m2.422 1.778h3.346v-1.694h1.022v1.694H49.3v.98h-3.332v2.072h2.926v.98h-6.832v-.98h2.884v-2.072H41.6zm6.888 5.432v5.628h-.98v-.672h-4.004v.672h-.994v-5.628zm-4.984 4.004h4.004v-3.052h-4.004zm8.792-10.612h9.338v5.418h-4.116v1.274h5.572v.952h-4.676c.994 1.26 2.66 2.394 4.984 3.374l-.686.882c-2.436-1.274-4.144-2.688-5.124-4.256h-.07v4.62h-1.036v-4.62h-.056c-1.064 1.722-2.8 3.136-5.236 4.242l-.574-.924q3.423-1.302 4.956-3.318H50.91v-.952h5.572v-1.274h-4.186zm8.344 4.536V192.3h-3.122v1.358zm-4.158 0V192.3h-3.178v1.358zm-3.178-2.24h3.178v-1.4h-3.178zm4.214-1.4v1.4h3.122v-1.4zm9.856 2.128c-.56 1.274-1.274 2.338-2.142 3.206l-.644-.826c1.316-1.372 2.212-3.234 2.674-5.572l1.022.182a19 19 0 0 1-.546 2.03h2.996v-2.492h1.022v2.492h4.606v.98h-4.606v3.234h4.326v.98h-4.326v3.556h5.502v1.008H64.924v-1.008h5.81v-3.556h-4.116v-.98h4.116v-3.234zm21.042-3.626a12.6 12.6 0 0 1 1.722 1.54l-.644.644h1.708v.966H86.89c.098 1.736.28 3.108.532 4.116.084.35.182.672.28.98.7-1.12 1.26-2.436 1.694-3.934l.91.392c-.56 1.848-1.288 3.416-2.184 4.676.196.448.406.84.63 1.162.462.672.84 1.008 1.12 1.008.252-.014.49-.742.7-2.184l.91.504c-.336 1.862-.812 2.8-1.442 2.8q-.882 0-1.89-1.26a7 7 0 0 1-.7-1.162 9.65 9.65 0 0 1-3.444 2.59l-.56-.84a9.4 9.4 0 0 0 3.556-2.772 16 16 0 0 1-.532-1.68c-.308-1.176-.504-2.646-.616-4.396h-4.802v2.324h3.43c-.028 2.366-.14 3.892-.322 4.578-.182.602-.616.91-1.316.938-.336 0-.742-.028-1.19-.056l-.308-.91a28 28 0 0 0 1.386.042c.308-.014.504-.224.588-.616.084-.476.126-1.484.154-3.024h-2.422v.42c-.056 2.506-.616 4.522-1.652 6.034l-.77-.686c.896-1.288 1.358-3.066 1.4-5.348v-4.662h5.782c-.028-.7-.042-1.414-.042-2.17h1.036c0 .784.014 1.498.042 2.17h2.618c-.42-.504-.98-1.022-1.694-1.568zm10.066-.014h1.036v2.52h5.026v6.58h-1.008v-.882h-4.018v4.774h-1.036v-4.774h-4.004v.882H93.47v-6.58h5.012zm-4.004 7.238h4.004v-3.738h-4.004zm5.04 0h4.018v-3.738h-4.018zm9.086 2.59c.308 0 .574.126.77.392.182.252.28.574.28.98q0 .924-.546 1.638a2.7 2.7 0 0 1-1.442.91v-.686c.364-.126.658-.336.882-.616.196-.294.308-.602.308-.938a.75.75 0 0 1-.308.056.81.81 0 0 1-.602-.252.85.85 0 0 1-.252-.616q0-.399.252-.63c.168-.168.378-.238.658-.238m19.992-9.772h.966v.91h3.472v.826h-3.472v.938h2.968v.812h-2.968V193h3.822v.84h-8.568V193h3.78v-.952h-2.828v-.812h2.828v-.938h-3.388v-.826h3.388zm-1.75 8.806v1.022h4.522v-1.022zm4.522-.784v-1.036h-4.522v1.036zm-4.522 2.59v2.226h-.966v-6.678h6.454v5.46c0 .756-.406 1.134-1.19 1.134h-1.176l-.238-.896 1.12.042c.336 0 .518-.168.518-.49v-.798zm-4.564-10.458c.938.7 1.722 1.414 2.366 2.128l-.686.7q-.882-1.008-2.394-2.142zm-1.694 4.13h2.968v6.146c.42-.392.854-.826 1.33-1.316l.266 1.064a19 19 0 0 1-2.492 2.17l-.392-.896c.21-.196.322-.406.322-.644v-5.558h-2.002zm21.56 3.5h-1.12l-.21-.938c.35.042.686.07.994.07.252 0 .392-.182.392-.532v-3.556h1.008v3.794c0 .77-.364 1.162-1.064 1.162m-5.74-4.018q-.609.714-1.26 1.302l-.63-.77c1.26-1.092 2.296-2.534 3.108-4.312l.98.21a16.4 16.4 0 0 1-1.218 2.254v5.348h-.98zm4.368-3.794.952.21c-.168.378-.35.742-.518 1.078h5.614v.63c-.182.7-.406 1.386-.686 2.058l-.91-.238c.238-.462.434-.966.588-1.526h-5.166a11.2 11.2 0 0 1-1.82 2.128l-.616-.756c1.05-.924 1.904-2.114 2.562-3.584m-4.606 8.54.966.196c-.336 1.344-.798 2.52-1.4 3.514l-.854-.546c.588-.952 1.008-2.016 1.288-3.164m6.888 4.116h-3.584c-.868 0-1.288-.448-1.288-1.344v-2.954h1.022v2.744c0 .392.182.602.574.602h3.08c.28 0 .476-.084.602-.252.168-.196.266-.756.322-1.68l.952.308c-.098 1.232-.28 1.974-.56 2.24-.238.224-.616.336-1.12.336m2.38-4.55c.896 1.232 1.568 2.31 2.044 3.234l-.826.588c-.504-1.008-1.19-2.114-2.044-3.318zm-5.04-3.906.812.378c-.532 1.078-1.148 2.016-1.848 2.8l-.77-.532q1.05-1.134 1.806-2.646m4.648-.014c.672.84 1.26 1.778 1.778 2.814l-.854.434a17 17 0 0 0-1.778-2.884zm-3.948 3.794q1.05 1.197 1.596 2.1l-.784.546a17 17 0 0 0-1.596-2.156zm7.658-7.252h6.79v.91h-2.94c-.112.742-.28 1.456-.504 2.156h3.08v7.84c0 .728-.336 1.106-.994 1.106h-.91l-.098-.364h-.616v-7.742h-.924v7.784h-.77v-7.784h-.952v8.162h-.868v-9.002h2.058c.224-.686.392-1.4.504-2.156h-2.856zm4.592 3.906v7.294l.56.014c.28 0 .42-.168.42-.49v-6.818zm2.352-1.792h3.402v-2.716h.994v2.716h1.134v.98h-1.134v7.504c0 .938-.462 1.414-1.386 1.414h-1.652l-.21-.952c.532.042 1.05.07 1.526.07s.728-.266.728-.77v-7.266H155.7zm1.036 2.562c.56.994 1.05 2.1 1.498 3.304l-.826.42c-.462-1.26-.966-2.38-1.526-3.36zm14.056 7h-3.122c-.98 0-1.456-.476-1.456-1.414v-8.036h1.008v7.826q0 .63.756.63h2.632c.336 0 .574-.14.714-.406.168-.336.294-1.316.364-2.954l.952.322c-.112 1.918-.308 3.08-.602 3.5-.266.35-.686.532-1.246.532m-6.65-7.952.966.196q-.588 3.612-1.68 6.216l-.868-.546c.728-1.778 1.246-3.738 1.582-5.866m9.016-.434a44 44 0 0 1 2.338 5.81l-.924.42c-.728-2.226-1.526-4.2-2.394-5.908zm-5.04-3.934c1.288 1.358 2.282 2.548 2.968 3.57l-.84.574c-.728-1.092-1.708-2.31-2.968-3.626zm10.206 4.074h4.172v-.882h1.008v.882h4.116v.882h-4.116v1.358h5.712v.882h-2.702v1.344h2.1v.882h-2.1v2.198c0 .77-.42 1.162-1.246 1.162h-2.03l-.21-.952c.672.042 1.302.07 1.89.07.392 0 .588-.182.588-.518v-1.96h-7.91v-.882h7.91v-1.344h-8.736v-.882h5.726v-1.358h-4.172zm1.638 5.74c.756.728 1.33 1.386 1.722 1.946l-.784.546c-.434-.602-1.022-1.26-1.75-1.988zm-1.134-7.994a10.5 10.5 0 0 1-1.162 1.778l-.868-.546a9.6 9.6 0 0 0 1.792-3.178l.952.21c-.112.308-.21.602-.322.882h3.584v.854h-2.366c.364.49.658.938.882 1.358l-.91.336c-.28-.588-.63-1.148-1.022-1.694zm5.684-.042a9.5 9.5 0 0 1-.784 1.288l-.84-.532a8 8 0 0 0 1.33-2.674l.952.196c-.084.294-.182.588-.28.868h4.032v.854h-2.534c.378.49.672.952.882 1.372l-.868.322a10.4 10.4 0 0 0-1.064-1.694zm8.946-1.918.882.406c-.728 1.54-1.89 2.912-3.514 4.102l-.294-.966c1.344-1.036 2.31-2.212 2.926-3.542m.518 3.22.868.406a11 11 0 0 1-1.26 2.142v7.084h-.966v-5.964a14 14 0 0 1-1.666 1.484l-.294-.966c1.512-1.218 2.618-2.618 3.318-4.186m4.718-3.164h.966v1.526h3.15v.91h-3.15v1.75h3.724v.924h-8.344v-.924h3.654v-1.75h-3.122v-.91h3.122zm-3.584 6.818h5.432v-1.036h1.008v1.036h1.638v.938h-1.638v3.598c0 .91-.462 1.372-1.358 1.372h-1.988l-.224-.966c.644.042 1.274.07 1.862.07.462 0 .7-.252.7-.728v-3.346h-5.432zm1.652 1.484c.784.798 1.386 1.498 1.806 2.086l-.784.56c-.448-.644-1.05-1.372-1.806-2.156z"
        />
        <defs>
            <linearGradient
                id="debugResultLoading_svg__b"
                x1={96.597}
                x2={86.149}
                y1={94.061}
                y2={119.044}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#32C5FF" stopOpacity={0.597} />
                <stop offset={1} stopColor="#B620E0" />
            </linearGradient>
            <clipPath id="debugResultLoading_svg__a">
                <path fill="#fff" d="M25 70h180v97.99H25z" />
            </clipPath>
        </defs>
    </svg>
);
export default SvgDebugResultLoading;
