/* eslint-disable complexity */
import {isNumber} from 'lodash';
import styled from '@emotion/styled';
import {Flex, Form, Select, Tag, Tooltip} from 'antd';
import {useMemo, useState, useCallback} from 'react';
import {
    ChoiceItem,
    UpdateDiffItem,
    UpdateCaseEvaluateScoreItem,
    apiEvaluateRecordUpdate,
    apiCaseEvaluateUpsert,
} from '@/api/ievalue/case';
import {CaseHistoryTypeEnum, CaseStatusEnum} from '@/constants/ievalue/case';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {useTaskInfo, useCaseStageType} from '@/hooks/ievalue/task';
import {
    TagContent,
    DiffTag,
} from '@/ievalue/TaskDetail/CaseProcessPanel/ModelResult/DiffTag';
import {getScoreItemByMetric} from '@/ievalue/TaskDetail/utils';
import {ScoreGroupProps} from '@/ievalue/TaskDetail/CaseProcessPanel/ModelResult/ScoreGroup';
import MetricAddTagPanel from '../../TagPanelComponent/MetricAddTagPanel';
import MetricAddNotePanel from '../MetricAddNotePanel';

const getScoreNameByScore = (choices: ChoiceItem[], score: number) => {
    return choices?.find(item => item.score === score)?.name ?? '';
};

const StyleFlex = styled(Flex)`
    .ant-5-form-css-var {
        --ant-5-form-item-margin-bottom: 0px !important;
    }
    .ant-5-form-item {
        font-weight: 500 !important;
        .ant-5-row > .ant-5-col > label {
            color: black !important;
        }
    }
    width: 100%;
`;

const ScoreSelect = ({
    groupInfo,
    recordItem,
    metricItem,
    blockHandle,
    groupCaseInfo,
    needUpdateRecord,
    handleAutoUpdateSort,
    handleRefresh,
}: ScoreGroupProps) => {
    const [taskInfo] = useTaskInfo();
    const stageType = useCaseStageType();
    const scoreSelectWidth = useMemo(
        () => {
            const maxLength = metricItem?.choices?.reduce((maxLength, item) => {
                return Math.max(maxLength, item.name.length);
            }, 3);
            return `${Math.max(maxLength * 23.5, 80)}px`;
        },
        [metricItem?.choices]
    );

    const originScoreItem = getScoreItemByMetric(
        metricItem.metric,
        recordItem.origin
    );
    const diffScoreItem = getScoreItemByMetric(
        metricItem.metric,
        recordItem?.diff
    );
    const [value, setValue] = useState(originScoreItem?.score);

    const handleChange = useCallback(
        async (updateScoreName: string, updateScore: number) => {
            if (blockHandle) {
                return;
            }
            const originScoreName = getScoreNameByScore(
                metricItem.choices,
                value
            );
            const diff: UpdateDiffItem[] = [
                {
                    type: CaseHistoryTypeEnum.RECORD,
                    caseID: groupCaseInfo.caseID,
                    groupID: groupCaseInfo.groupID,
                    recordID: recordItem.origin.predictRecordID,
                    field: metricItem.metric,
                    origin: originScoreName,
                    turnTo: updateScoreName,
                },
            ];
            const score: UpdateCaseEvaluateScoreItem[] = [
                {
                    metric: metricItem.metric,
                    scoreName: updateScoreName,
                    score: updateScore,
                    noScoring: !!metricItem.noScoring,
                    notRequired: !!metricItem.notRequired,
                },
            ];

            if (needUpdateRecord) {
                setValue(updateScore);
            }

            try {
                const apiCall = needUpdateRecord
                    ? apiEvaluateRecordUpdate({
                        ID: recordItem.origin.ID,
                        predictRecordID: recordItem.origin.predictRecordID,
                        evaluateCaseID: recordItem.origin.evaluateCaseID,
                        taskID: taskInfo.ID,
                        score,
                        diff,
                    })
                    : apiCaseEvaluateUpsert({
                        accepted: 0,
                        predictRecordID: recordItem.origin.predictRecordID,
                        stageID: groupCaseInfo.stageID,
                        groupID: groupCaseInfo.groupID,
                        caseID: groupCaseInfo.caseID,
                        taskID: taskInfo.ID,
                        score,
                        diff,
                    });

                await Promise.all([
                    apiCall,
                    handleAutoUpdateSort?.(metricItem.metric, updateScore),
                ]);
            } catch (error) {
                console.error('Score update failed:', error);
            } finally {
                await handleRefresh?.();
            }
        },
        [
            blockHandle,
            value,
            metricItem,
            needUpdateRecord,
            taskInfo.ID,
            groupCaseInfo,
            recordItem.origin,
            handleAutoUpdateSort,
            handleRefresh,
        ]
    );

    const handleSelect = useCallback(
        (_: number, option: ChoiceItem) => {
            const {name = '', score = null} = option ?? {};
            handleChange(name, score);
        },
        [handleChange]
    );

    return (
        <StyleFlex align="center" wrap gap={4}>
            <Form.Item
                label={metricItem.desc}
                required={!metricItem?.notRequired}
            >
                <Select<any, any>
                    size="small"
                    style={{width: scoreSelectWidth}}
                    placeholder="请选择"
                    {...([
                        TaskStageEnum.ACCEPTING,
                        TaskStageEnum.AUDITING,
                    ].includes(stageType)
                    || (stageType === TaskStageEnum.EVALUATING
                        && groupInfo.status === CaseStatusEnum.REJECTED)
                    || blockHandle
                        ? {
                            value,
                        }
                        : {
                            defaultValue:
                                  stageType === TaskStageEnum.AUDITING_FORWARD
                                      ? diffScoreItem?.score ?? value
                                      : value,
                        })}
                    fieldNames={{label: 'name', value: 'score'}}
                    options={metricItem.choices}
                    onChange={handleSelect}
                    allowClear={metricItem?.notRequired}
                    disabled={taskInfo.stage === 'TERMINATED' || blockHandle}
                />
            </Form.Item>
            <TagContent>
                {stageType !== TaskStageEnum.AUDITING_FORWARD
                    && recordItem.diff && (
                    <DiffTag
                        blockHandle={blockHandle}
                        handleChange={handleChange}
                        recordItem={recordItem}
                        metric={metricItem.metric}
                        groupInfo={groupInfo}
                        stageType={stageType}
                    />
                )}
                {stageType === TaskStageEnum.AUDITING_FORWARD
                    && isNumber(diffScoreItem?.score)
                    && diffScoreItem?.score !== originScoreItem?.score && (
                    <Tooltip title="上一轮得分">
                        <Tag>{originScoreItem?.scoreName}</Tag>
                    </Tooltip>
                )}
            </TagContent>
            <MetricAddTagPanel
                blockHandle={blockHandle}
                refresh={handleRefresh}
                metricItem={metricItem}
                predictRecordID={recordItem.origin.predictRecordID}
                caseID={groupCaseInfo.caseID}
                stageID={groupCaseInfo.stageID}
                groupID={groupCaseInfo.groupID}
                taskID={taskInfo.ID}
                selectList={originScoreItem?.tags}
            />
            <MetricAddNotePanel
                predictRecordID={recordItem.origin.predictRecordID}
                note={originScoreItem?.note}
                refresh={handleRefresh}
                metricItem={metricItem}
            />
        </StyleFlex>
    );
};

export default ScoreSelect;
