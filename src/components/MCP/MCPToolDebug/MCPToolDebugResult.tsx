import {Flex} from 'antd';
import {ReactElement, useMemo} from 'react';
import debugResultLoading from '@/assets/mcp/debugResultLoading.svg';
import debugResultPlaceholder from '@/assets/mcp/debugResultPlaceholder.svg';
import JSONViewer from '../JSONViewer';
import {useMCPToolDebugContext} from './Providers/MCPToolDebugProvider';

interface Props {
  // 用来标记到底是在全屏弹窗中使用，还是在页面内使用，页面内即为true，二者布局略有不同
  card?: boolean;
}

const ImgContainer = (props: {children: ReactElement}) => (
    <Flex align="center" justify="center" style={{height: '100%'}}>
        {props.children}
    </Flex>
);

export function MCPToolDebugResult({card = true}: Props) {
    const {debugResult, debugLoading} = useMCPToolDebugContext();
    // 不确定后端到底返回个啥，总之如果是合法json，就返回json，否则转字符串
    const validContent = useMemo(
        () => {
            if (typeof debugResult === 'string' || debugResult === null || debugResult === undefined) {
                return debugResult ?? '请先输入变量运行后，查看结果';
            }
            try {
                JSON.parse(JSON.stringify(debugResult));
                return debugResult;
            } catch (e) {
                return debugResult.toString();
            }
        },
        [debugResult]
    );
    if (debugLoading && !card) {
        return (
            <ImgContainer>
                <img src={debugResultLoading} alt="loading" />
            </ImgContainer>
        );
    } else if (!debugResult && !card) {
        return (
            <ImgContainer>
                <img src={debugResultPlaceholder} alt="placeholder" style={{marginTop: '-50px'}} />
            </ImgContainer>
        );
    }
    return (
        <Flex
            className="mcp-tool-debug-result-container"
            style={{position: 'relative', height: '100%'}}
            vertical
        >
            <JSONViewer json={validContent} />

        </Flex>
    );
}
