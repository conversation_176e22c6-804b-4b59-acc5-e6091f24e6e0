/* eslint-disable max-lines */
import {<PERSON><PERSON>, <PERSON>, Mo<PERSON>, Row, Typography} from 'antd';
import {cloneElement, ReactElement, useCallback, useMemo, useState} from 'react';
import {SettingOutlined} from '@ant-design/icons';
import styled from '@emotion/styled';
import {Button, message} from '@panda-design/components';
import {UserAvatarList} from '@/components/MCP/UserAvatarList';
import {MCPGlobalVarsForm} from './MCPGlobalVarsForm';
import {GlobalVar, useMCPGlobalVarsContext} from './Providers/MCPServerConfigProvider';

interface Props {
  content?: (props: {open: () => void}) => ReactElement;
  children?: ReactElement;
  onSuccess?: (values: GlobalVar[]) => void;
    modalVisible?: boolean;
    openModal?: () => void;
    closeModal?: (newVars?: GlobalVar[]) => void;
}

const DefaultContent = (
    <Button
        color="primary"
        variant="link"
        icon={<SettingOutlined />}
        iconPosition="start"
        style={{paddingLeft: 0, paddingRight: 0}}
    >服务配置
    </Button>
);

const StyledRow = styled(Row)`
  line-height: 32px;
  margin-bottom: 24px;
`;

const StyledCol = styled(Col)`
    color: var(--color-gray-8);
    padding-left: 8px;
    padding-right: 17px;
    display: flex;
    align-items: center;
`;

const LabelCol = ({text}: {text: string}) => {
    return (
        <StyledCol>
            <Typography.Text style={{width: '100px'}} ellipsis={{tooltip: text}}>{text}</Typography.Text>
        </StyledCol>
    );
};

export function MCPServerConfigButton({
    children = DefaultContent,
    onSuccess,
    // 如果传入这几个参数，表示modal由外部控制
    modalVisible,
    openModal,
    closeModal,
}: Props) {
    const [open, setOpen] = useState(false);
    const {saveGlobalVars, mcpServerInfo, inPlayground} = useMCPGlobalVarsContext();
    const authDescription = mcpServerInfo?.serverConf.serverExtension?.authDescription;
    const contacts = mcpServerInfo?.contacts ?? [];
    const serverName = mcpServerInfo?.name;
    const mixOpen = useMemo(
        () => {
            return modalVisible ?? open;
        },
        [modalVisible, open]
    );
    const openHandler = useMemo(
        () => {
            return openModal ? openModal : () => setOpen(true);
        },
        [openModal, setOpen]
    );
    const closeHandler = useCallback(
        (newVars?: GlobalVar[]) => {
            if (closeModal) {
                // 这个函数会给react事件系统调，所以传入的参数有可能是event，需要校验
                if (newVars && Array.isArray(newVars)) {
                    closeModal(newVars);
                }
                closeModal();
            } else {
                setOpen(false);
            }
        },
        [closeModal, setOpen]
    );
    const submit = useCallback(
        async () => {
            const res = await saveGlobalVars(onSuccess);
            if (!(res instanceof Error) && Array.isArray(res)) {
                message.success('配置成功');
                closeHandler(res);
            } else {
                // TODO 这里可以区分是表单验证错误还是接口错误，分别处理
            }
        },
        [saveGlobalVars, closeHandler, onSuccess]
    );
    return (
        <>
            {cloneElement(children, {onClick: openHandler})}
            <Modal
                title="服务配置"
                open={mixOpen}
                onOk={submit}
                // @ts-ignore
                onCancel={closeHandler}
                centered
                forceRender
            >
                <Alert message="填写全局变量参数才能试用工具，同时，ServerConfig将同步更新。" type="info" />
                <div style={{marginTop: '12px'}}>
                    {inPlayground && (
                        <StyledRow>
                            <LabelCol text="MCP Server" />
                            <Col>{serverName}</Col>
                        </StyledRow>
                    )}
                    {authDescription && (
                        <StyledRow>
                            <LabelCol text="鉴权方法" />
                            <Col>{authDescription}</Col>
                        </StyledRow>
                    )}
                    <StyledRow>
                        <LabelCol text="联系人" />
                        <Col>
                            {
                                contacts.length > 0 ? <UserAvatarList users={contacts} max={2} /> : <span>暂无数据</span>
                            }
                        </Col>
                    </StyledRow>
                </div>
                <MCPGlobalVarsForm />
            </Modal>
        </>
    );
}
