import {Collapse, Flex, Modal, Typography} from 'antd';
import styled from '@emotion/styled';
import {MCPGlobalVarsForm} from './MCPGlobalVarsForm';
import {MCPToolParamsForm} from './MCPToolParamsForm';
import {MCPToolDebugButton} from './MCPToolDebugButton';
import {MCPToolDebugResult} from './MCPToolDebugResult';

interface Props{
  open: boolean;
  onCancel: () => void;
}
const items = [
    {
        key: '1',
        label: '服务配置',
        children: <MCPGlobalVarsForm />,
    },
];

const Title = styled.div`
  background: linear-gradient(to right, #F5F7FA, #F5F7FA4C);
  padding: 6px 12px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  margin: 16px 0px;
`;

const StyledCollapse = styled(Collapse)`
  .ant-5-collapse-header{
    background: linear-gradient(to right, #F5F7FA, #F5F7FA4C);
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
    margin: 16px 0px;
  }
  .ant-5-collapse-content-hidden{
    display: block;
    height: 0px;
    overflow: hidden;
  }
`;

const ResultContainer = styled.div`
    height: calc(100vh - 120px);
    margin-top: 16px;
    .mcp-tool-debug-result-container{
        .json-viewer-container{
            height: 1px;
            flex-grow: 1;
        }
    }
`;

const LeftContainer = styled.div`
    position: relative;
    flex-grow: 1;
    height: 100%;
    border-right: 1px solid #E8E8E8;
    min-width: 400px;
`;

const RightContainer = styled.div`
    flex-grow: 2;
    height: 100%;
    padding-left: 20px;
`;

export function MCPToolDebugModal({open, onCancel}: Props) {
    return (
        <Modal
            title="调试"
            open={open}
            onCancel={onCancel}
            footer={null}
            width="100vw"
            height="100vh"
            styles={{
                content: {height: '100vh'},
                mask: {background: 'transparent'},
                body: {height: 'calc(100vh - 75px)'},
            }}
            style={{maxWidth: '100vw', overflow: 'hidden', top: 0}}
            centered
        >
            <Flex style={{height: '100%'}}>
                <LeftContainer>
                    <Typography.Text
                        type="secondary"
                        style={{fontSize: '14px'}}
                    >请先进行服务配置，再输入工具参数开始调试
                    </Typography.Text>
                    <div style={{overflow: 'auto', paddingRight: '20px', height: 'calc(100vh - 140px)'}}>
                        <div>
                            <Title>工具参数</Title>
                            <div style={{padding: '12px 16px'}}>
                                <MCPToolParamsForm />
                            </div>
                        </div>
                        <StyledCollapse
                            style={{marginTop: '16px'}}
                            defaultActiveKey={['1']}
                            ghost
                            items={items}
                            expandIconPosition="end"
                            collapsible="icon"
                        />
                    </div>
                    <Flex
                        justify="center"
                        style={{paddingRight: '20px', position: 'absolute', bottom: 0, left: 0, width: '100%'}}
                    >
                        <MCPToolDebugButton />
                    </Flex>
                </LeftContainer>
                <RightContainer>
                    <Typography.Text
                        style={{fontSize: '14px', lineHeight: '22px', fontWeight: '500'}}
                    >运行结果
                    </Typography.Text>
                    <ResultContainer><MCPToolDebugResult card={false} /></ResultContainer>
                </RightContainer>
            </Flex>
        </Modal>
    );
}
