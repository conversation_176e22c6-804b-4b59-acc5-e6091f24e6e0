import {useBoolean} from 'huse';
import {Button} from '@panda-design/components';
import {useCallback} from 'react';
import {IconDebug} from '@/icons/mcp';
import {MCPToolDebugModal} from './MCPToolDebugModal';

interface Props{
  blocker: () => Promise<boolean>;
  [key: string]: any;
}

export function MCPToolDebugModalButton({blocker, ...resetProps}: Props) {
    const [open, {on, off}] = useBoolean(false);
    const handlerDebug = useCallback(
        async () => {
            const isBlocked = await blocker();
            if (isBlocked) {
                return;
            }
            on();
        },
        [blocker, on]
    );
    return (
        <>
            <Button
                type="text"
                icon={<IconDebug />}
                iconPosition="start"
                onClick={handlerDebug}
                {...resetProps}
            >调试
            </Button>
            <MCPToolDebugModal open={open} onCancel={off} />
        </>
    );
}
