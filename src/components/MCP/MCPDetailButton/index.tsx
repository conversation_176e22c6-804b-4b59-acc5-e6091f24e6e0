import {Button, ButtonProps} from '@panda-design/components';
import {use<PERSON><PERSON>back, MouseEvent, CSSProperties} from 'react';
import {IconDetail} from '@/icons/mcp';
import {MCPDetailLink} from '@/links/mcp';

interface Props {
    serverId: number;
    size?: ButtonProps['size'];
    style?: CSSProperties;
}

export const MCPDetailButton = ({serverId, size, style}: Props) => {
    const handleClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            const url = MCPDetailLink.toUrl({mcpId: serverId});
            window.open(url);
        },
        [serverId]
    );

    return (
        <Button
            style={{padding: 0, gap: 3, ...style}}
            onClick={handleClick}
            icon={<IconDetail />}
            type="text"
            size={size}
        >
            详情
        </Button>
    );
};
